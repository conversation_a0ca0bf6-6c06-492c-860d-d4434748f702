// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		0E2632D12E1A17D4006D47FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0E2632AF2E1A17D3006D47FE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0E2632B62E1A17D3006D47FE;
			remoteInfo = TurnMeOnGame;
		};
		0E2632DB2E1A17D4006D47FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0E2632AF2E1A17D3006D47FE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0E2632B62E1A17D3006D47FE;
			remoteInfo = TurnMeOnGame;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0E2632B72E1A17D3006D47FE /* TurnMeOnGame.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TurnMeOnGame.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0E2632D02E1A17D4006D47FE /* TurnMeOnGameTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TurnMeOnGameTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		0E2632DA2E1A17D4006D47FE /* TurnMeOnGameUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TurnMeOnGameUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		0E2632B92E1A17D3006D47FE /* TurnMeOnGame */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TurnMeOnGame;
			sourceTree = "<group>";
		};
		0E2632D32E1A17D4006D47FE /* TurnMeOnGameTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TurnMeOnGameTests;
			sourceTree = "<group>";
		};
		0E2632DD2E1A17D4006D47FE /* TurnMeOnGameUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TurnMeOnGameUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		0E2632B42E1A17D3006D47FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632CD2E1A17D4006D47FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632D72E1A17D4006D47FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0E2632AE2E1A17D3006D47FE = {
			isa = PBXGroup;
			children = (
				0E2632B92E1A17D3006D47FE /* TurnMeOnGame */,
				0E2632D32E1A17D4006D47FE /* TurnMeOnGameTests */,
				0E2632DD2E1A17D4006D47FE /* TurnMeOnGameUITests */,
				0E2632B82E1A17D3006D47FE /* Products */,
			);
			sourceTree = "<group>";
		};
		0E2632B82E1A17D3006D47FE /* Products */ = {
			isa = PBXGroup;
			children = (
				0E2632B72E1A17D3006D47FE /* TurnMeOnGame.app */,
				0E2632D02E1A17D4006D47FE /* TurnMeOnGameTests.xctest */,
				0E2632DA2E1A17D4006D47FE /* TurnMeOnGameUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0E2632B62E1A17D3006D47FE /* TurnMeOnGame */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E2632E42E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGame" */;
			buildPhases = (
				0E2632B32E1A17D3006D47FE /* Sources */,
				0E2632B42E1A17D3006D47FE /* Frameworks */,
				0E2632B52E1A17D3006D47FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				0E2632B92E1A17D3006D47FE /* TurnMeOnGame */,
			);
			name = TurnMeOnGame;
			packageProductDependencies = (
			);
			productName = TurnMeOnGame;
			productReference = 0E2632B72E1A17D3006D47FE /* TurnMeOnGame.app */;
			productType = "com.apple.product-type.application";
		};
		0E2632CF2E1A17D4006D47FE /* TurnMeOnGameTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E2632E72E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGameTests" */;
			buildPhases = (
				0E2632CC2E1A17D4006D47FE /* Sources */,
				0E2632CD2E1A17D4006D47FE /* Frameworks */,
				0E2632CE2E1A17D4006D47FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0E2632D22E1A17D4006D47FE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				0E2632D32E1A17D4006D47FE /* TurnMeOnGameTests */,
			);
			name = TurnMeOnGameTests;
			packageProductDependencies = (
			);
			productName = TurnMeOnGameTests;
			productReference = 0E2632D02E1A17D4006D47FE /* TurnMeOnGameTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		0E2632D92E1A17D4006D47FE /* TurnMeOnGameUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0E2632EA2E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGameUITests" */;
			buildPhases = (
				0E2632D62E1A17D4006D47FE /* Sources */,
				0E2632D72E1A17D4006D47FE /* Frameworks */,
				0E2632D82E1A17D4006D47FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				0E2632DC2E1A17D4006D47FE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				0E2632DD2E1A17D4006D47FE /* TurnMeOnGameUITests */,
			);
			name = TurnMeOnGameUITests;
			packageProductDependencies = (
			);
			productName = TurnMeOnGameUITests;
			productReference = 0E2632DA2E1A17D4006D47FE /* TurnMeOnGameUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0E2632AF2E1A17D3006D47FE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					0E2632B62E1A17D3006D47FE = {
						CreatedOnToolsVersion = 16.4;
					};
					0E2632CF2E1A17D4006D47FE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 0E2632B62E1A17D3006D47FE;
					};
					0E2632D92E1A17D4006D47FE = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 0E2632B62E1A17D3006D47FE;
					};
				};
			};
			buildConfigurationList = 0E2632B22E1A17D3006D47FE /* Build configuration list for PBXProject "TurnMeOnGame" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0E2632AE2E1A17D3006D47FE;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 0E2632B82E1A17D3006D47FE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0E2632B62E1A17D3006D47FE /* TurnMeOnGame */,
				0E2632CF2E1A17D4006D47FE /* TurnMeOnGameTests */,
				0E2632D92E1A17D4006D47FE /* TurnMeOnGameUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0E2632B52E1A17D3006D47FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632CE2E1A17D4006D47FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632D82E1A17D4006D47FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0E2632B32E1A17D3006D47FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632CC2E1A17D4006D47FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0E2632D62E1A17D4006D47FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		0E2632D22E1A17D4006D47FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0E2632B62E1A17D3006D47FE /* TurnMeOnGame */;
			targetProxy = 0E2632D12E1A17D4006D47FE /* PBXContainerItemProxy */;
		};
		0E2632DC2E1A17D4006D47FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 0E2632B62E1A17D3006D47FE /* TurnMeOnGame */;
			targetProxy = 0E2632DB2E1A17D4006D47FE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0E2632E22E1A17D4006D47FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		0E2632E32E1A17D4006D47FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		0E2632E52E1A17D4006D47FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGame;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0E2632E62E1A17D4006D47FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UIStatusBarHidden = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGame;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		0E2632E82E1A17D4006D47FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGameTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TurnMeOnGame.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TurnMeOnGame";
			};
			name = Debug;
		};
		0E2632E92E1A17D4006D47FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGameTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TurnMeOnGame.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TurnMeOnGame";
			};
			name = Release;
		};
		0E2632EB2E1A17D4006D47FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGameUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TurnMeOnGame;
			};
			name = Debug;
		};
		0E2632EC2E1A17D4006D47FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.amazingsauce.TurnMeOnGameUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TurnMeOnGame;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0E2632B22E1A17D3006D47FE /* Build configuration list for PBXProject "TurnMeOnGame" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E2632E22E1A17D4006D47FE /* Debug */,
				0E2632E32E1A17D4006D47FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E2632E42E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGame" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E2632E52E1A17D4006D47FE /* Debug */,
				0E2632E62E1A17D4006D47FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E2632E72E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGameTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E2632E82E1A17D4006D47FE /* Debug */,
				0E2632E92E1A17D4006D47FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0E2632EA2E1A17D4006D47FE /* Build configuration list for PBXNativeTarget "TurnMeOnGameUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0E2632EB2E1A17D4006D47FE /* Debug */,
				0E2632EC2E1A17D4006D47FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0E2632AF2E1A17D3006D47FE /* Project object */;
}
